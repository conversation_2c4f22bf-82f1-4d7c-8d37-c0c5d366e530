plugins:
  - rubocop-performance
  - rubocop-rails

AllCops:
  SuggestExtensions: false
  NewCops: enable
  Exclude:
    - 'db/**/*'
    - 'spec/**/*'
    - 'bin/*'
    - 'vendor/**/*'
    - 'config/*.rb'
    - 'config/initializers/filter_parameter_logging.rb'
    - 'config/initializers/content_security_policy.rb'
    - 'config/environments/production.rb'
    - 'config.ru'
    - 'Rakefile'
    - 'Gemfile'

Style/StringLiterals:
  Exclude:
    - 'config/environments/*.rb'

Style/Documentation:
  Enabled: false

# Use `[a, [b, c]]` not `[ a, [ b, c ] ]`
Layout/SpaceInsideArrayLiteralBrackets:
  Enabled: false

Style/FrozenStringLiteralComment:
  Enabled: false

