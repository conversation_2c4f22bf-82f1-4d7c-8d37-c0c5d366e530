module Admin
  class ErrorsController < BaseController
    before_action :set_service, :set_items

    def index; end

    def show
      @item   = @items.find { |i| i['id'].to_s == params[:id] }
      @errors = cached_errors
      @error  = params['item'].present? ? @errors.find { |i| i['id'] == params['item'].to_i } : @errors.first
    end

    private

    def set_service
      @service = Admin::RollbarService.new
    end

    def set_items
      @items = Rails.cache.fetch(:error_items, expires_in: 10.minutes) { @service.items }
    end

    def cached_errors
      Rails.cache.fetch("errors_#{params[:id]}", expires_in: 5.minutes) { @service.instances(params[:id]) }
    end
  end
end
