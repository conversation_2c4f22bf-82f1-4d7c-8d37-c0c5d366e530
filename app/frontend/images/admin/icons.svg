<svg xmlns="http://www.w3.org/2000/svg">
  <symbol id="menu" fill="currentColor" viewBox="0 0 20 20">
    <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
  </symbol>
  <symbol id="mini-arrow" aria-hidden="true" stroke="currentColor" fill="none" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v13m0-13 4 4m-4-4-4 4"></path>
  </symbol>
  <symbol id="check" aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 11.917 9.724 16.5 19 7.5"/>
  </symbol>
  <symbol id="uncheck" aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <rect width="12" height="12" x="6" y="6" stroke="currentColor" stroke-linejoin="round" stroke-width="2" rx="1"/>
  </symbol>
  <symbol id="link" aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.213 9.787a3.391 3.391 0 0 0-4.795 0l-3.425 3.426a3.39 3.39 0 0 0 4.795 4.794l.321-.304m-.321-4.49a3.39 3.39 0 0 0 4.795 0l3.424-3.426a3.39 3.39 0 0 0-4.794-4.795l-1.028.961"/>
  </symbol>
  <symbol id="edit" aria-hidden="true" viewBox="0 0 20 20">
    <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path>
    <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path>
  </symbol>
  <symbol id="restore" aria-hidden="true" viewBox="0 0 24 24">
    <path d="M14.502 7.046h-2.5v-.928a2.122 2.122 0 0 0-1.199-1.954 1.827 1.827 0 0 0-1.984.311L3.71 8.965a2.2 2.2 0 0 0 0 3.24L8.82 16.7a1.829 1.829 0 0 0 1.985.31 2.121 2.121 0 0 0 1.199-1.959v-.928h1a2.025 2.025 0 0 1 1.999 2.047V19a1 1 0 0 0 1.275.961 6.59 6.59 0 0 0 4.662-7.22 6.593 6.593 0 0 0-6.437-5.695Z"/>
  </symbol>
  <symbol id="trash" viewBox="0 0 20 20">
    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
  </symbol>
  <symbol id="rails" viewBox="0 0 64 64" fill="currentColor">
    <path d="m34.1426 39.3015 3.733 1.6917-.3806-2.6338-3.3953-1.8102zm-28.31666 8.8156 4.86066.548 1.0596-4.4723-4.64613-.8167zm17.29096-30.9526-3.1791-2.3263-2.2123 2.975 3.4918 2.162zm9.7462-7.83097-2.0389-2.63212-2.8682 1.67579 2.3857 2.565zm10.5057-1.84726-.6558-2.1513-3.4542-.274 1.0007 2.36343zm11.6834 2.70283-2.9021-2.05761v1.33285l2.7984 1.71116zm-39.228 19.1125-3.9743-1.7111-1.3474 3.8889 4.0779 1.5043zm19.7891-1.7677-.7148 2.4111 2.5572 2.3264.6415-2.2362zm16.3295-10.7831 2.1247.1026.0179-.9758-2.2123-.4437zm-8.842-8.13319c-10.218-.48612-28.5204 9.09309-31.1473 47.54439h25.0715c-5.6201-14.4051-4.9696-25.2783-1.1043-32.639 5.6218-10.7124 13.9867-11.4212 21.8012-6.9065.277-.6487.6559-1.4707.6559-1.4707-4.4639-4.239-8.6366-6.21-15.277-6.52819zm.7505 7.98119.931 2.2044 1.7281-.921-.638-2.0347zm-5.6647 5.008 1.9013 2.5843 1.1741-1.6934-1.6584-2.5473zm2.9038 30.051-1.1758-2.8283-4.337-.8025 1.1418 3.2473z"/>
  </symbol>
  <symbol id="sidekiq" viewBox="0 0 64 64" fill="currentColor">
    <path d="M18.3363 29.6679C18.3363 29.6679 17.5967 29.5154 17.379 29.5804C17.1613 29.6454 15.9199 29.4291 15.3768 29.0366C14.8337 28.6441 14.2881 28.9291 13.7876 28.6241C13.287 28.319 12.5237 28.3615 11.9581 28.3415C11.3925 28.3215 10.6729 28.3628 10.4114 28.7103C10.1499 29.0579 9.13375 30.1242 8.75834 30.558C8.38293 30.9918 6.88129 32.5582 6.29816 32.7969C6.29816 32.7969 6.45082 33.037 6.36323 33.2107C6.27563 33.3845 6.34195 33.8833 6.34195 34.0583C6.34195 34.2333 6.38575 34.5146 6.23434 34.6896C6.10138 34.8504 6.01793 35.0462 5.99407 35.2534C5.99407 35.3622 5.81888 35.7534 5.99407 36.276C6.16926 36.7985 6.31943 37.3836 5.6024 37.1448C4.88536 36.906 5.03553 36.2547 4.44864 36.2322C3.86175 36.2097 3.09716 36.2372 2.87692 35.4284C2.65668 34.6196 2.25124 33.2545 2.16114 33.0369C2.07104 32.8194 1.76946 31.8193 2.33508 30.9505C2.9007 30.0817 3.00957 29.4504 3.09716 29.2541C3.18476 29.0579 3.59771 27.819 3.90179 27.644C4.20587 27.469 4.09825 26.9277 4.2772 26.6889C4.45615 26.4501 4.38607 26.0576 4.49494 25.8401C4.60381 25.6226 4.6476 24.9925 4.79902 24.515C4.95043 24.0374 5.45223 23.2961 5.75757 22.9061C6.0116 22.5621 6.17472 22.1596 6.23183 21.736C6.23183 21.5397 6.12296 21.1284 6.62351 20.7796C7.12406 20.4309 8.01628 19.302 8.10388 19.1283C8.10388 19.0845 8.10388 18.2807 8.05883 17.9969C8.01378 17.7131 7.95121 17.4544 8.23402 17.3456C8.46684 17.2455 8.69223 17.1289 8.9085 16.9968C8.66856 16.7269 8.37604 16.5087 8.04882 16.3555C7.46067 16.063 5.6975 14.0728 5.53482 12.9002C5.37214 11.7276 5.30582 9.85995 6.90632 9.21114C8.07889 8.74546 9.29436 8.39584 10.5353 8.1673C10.7968 8.1348 11.7103 7.93978 12.0707 8.19981C12.0707 8.19981 11.6778 8.1348 11.5151 8.19981C11.3524 8.26481 12.2985 8.23356 12.5275 8.49358C12.2943 8.42539 12.0521 8.39252 11.8092 8.39607C11.5476 8.42858 12.2659 8.72235 12.3973 8.94987C12.5287 9.17739 13.023 9.34241 12.9517 9.69994C12.8803 10.0575 12.8541 10.6125 12.9191 10.775C13.0625 11.0607 13.1512 11.3706 13.1807 11.6889C13.2144 12.1586 13.3134 12.6214 13.4747 13.064C13.5999 13.3578 13.9327 14.2703 13.8013 14.9229C13.8013 14.9229 13.9265 15.2492 14.5847 14.4991C15.2429 13.749 15.8924 13.624 16.2115 13.624C16.5306 13.624 17.0286 13.2965 17.3552 13.4278C17.6818 13.559 18.0422 13.3953 18.4339 13.4928C18.8256 13.5903 20.4574 13.4928 20.8115 13.3678C21.1656 13.2427 22.4107 13.0752 22.671 13.0752C22.9313 13.0752 22.3457 12.6514 22.8349 12.3252C23.3242 11.9989 22.3119 11.8364 23.0639 10.8913C23.816 9.94621 23.0952 9.68368 22.9663 9.48867C22.8375 9.29365 22.8024 8.67235 22.8024 8.31482C22.8024 7.95728 22.7048 7.04221 22.7048 7.04221L22.8299 6.87969C22.7754 6.78914 22.7045 6.70946 22.621 6.64467C22.5184 6.58342 22.4145 6.21338 22.3332 5.96711C22.2518 5.72084 21.3446 4.38448 21.0818 4.17946C20.819 3.97444 20.7727 3.66566 20.814 3.37814C20.8553 3.09061 21.0643 2.84309 21.0192 2.53432C20.9742 2.22554 20.9992 1.81675 20.9779 1.67299C20.9566 1.52923 21.0192 0.665405 21.2044 0.584148C21.2044 0.584148 21.3483 0.521642 21.4109 0.72666C21.4109 0.72666 21.307 0.00784787 21.536 0.00784787C21.536 0.00784787 21.8038 -0.0746593 21.9114 0.295373C22.0191 0.665405 22.1217 0.859171 22.1479 0.95793C22.1735 1.04978 22.2079 1.13895 22.2506 1.2242C22.3807 1.16746 22.5272 1.16035 22.6623 1.2042C22.8475 1.28671 22.9713 1.36797 22.9713 1.47172C22.9713 1.57548 22.9713 1.38922 23.1978 1.38922C23.4243 1.38922 23.6308 1.32671 23.6921 1.55298C23.7536 1.75433 23.7955 1.96114 23.8173 2.17053C23.8298 2.36952 23.879 2.56448 23.9624 2.74558C24.0969 3.00571 24.1673 3.29414 24.1677 3.58691C24.1677 3.97819 24.3116 4.697 24.3741 5.02578C24.4367 5.35456 25.135 6.62842 25.135 6.62842C25.135 6.62842 26.2049 6.83469 26.6992 6.73093C27.1935 6.62717 28.0569 6.81344 27.9505 7.26597C27.8442 7.71851 27.7866 9.05363 27.7453 10.1012C27.704 11.1488 27.5388 13.1827 27.3699 13.9028C27.201 14.6229 26.1974 16.4918 25.7857 16.7993C25.374 17.1068 24.9222 17.3131 23.7897 17.8481C22.6573 18.3832 21.3408 18.9732 21.3408 18.9732C21.3408 18.9732 22.0416 19.9583 22.2055 20.0208C22.3694 20.0833 22.3494 19.8771 22.5546 19.9583C22.6573 19.9996 23.0114 20.1383 22.7861 20.0458C22.7861 20.0546 24.0262 20.3721 24.0262 20.3721C24.1451 20.3721 24.219 20.3484 24.2039 20.2771C24.1414 19.9908 24.4304 19.6208 24.5531 19.5383C24.6757 19.4558 24.3679 19.9133 24.5531 20.1133C24.7383 20.3134 27.3987 19.532 27.3486 19.6733C27.2986 19.8146 27.4938 19.8258 27.6627 19.6933C27.8317 19.5608 28.5387 19.102 29.0392 19.1045C29.5398 19.107 30.6823 19.207 30.9876 19.0957C31.2929 18.9845 32.6819 18.1294 33.1462 17.9031C33.6105 17.6769 35.2085 17.6981 35.8829 17.2443C36.5574 16.7905 38.0403 15.7355 38.666 15.3442C39.2917 14.9529 43.9517 12.1101 44.9441 11.9614C45.9364 11.8126 48.3165 10.8688 48.7244 10.5788C49.1324 10.2887 50.415 9.57868 50.6716 9.36241C50.9281 9.14614 52.2158 9.70369 52.8865 10.6563C52.8865 10.6563 54.1942 10.0162 54.477 9.99246C54.7598 9.96871 55.2803 9.60493 55.6795 9.29865C56.0787 8.99237 57.2875 8.18105 57.8144 7.92353C57.8144 7.92353 58.0809 7.80602 58.1322 7.61976C58.1835 7.43349 58.3963 7.07596 58.5877 7.14971C58.5877 7.14971 59.0082 6.61467 59.1784 6.77468C59.1784 6.77468 59.5475 6.4259 59.759 6.61467C59.759 6.61467 59.968 6.64467 60.038 6.75718C60.038 6.75718 60.3033 6.54341 60.4272 6.70593C60.4272 6.70593 60.6187 6.48966 60.9415 6.74343C60.9415 6.74343 61.1568 7.0072 60.7613 7.49349C60.5744 7.61102 60.4274 7.78228 60.3396 7.98479C60.1669 8.34107 59.6852 9.01988 59.1571 9.2849C58.629 9.54992 58.0171 10.0175 58.0396 10.9713C58.0621 11.9251 57.6554 13.0365 56.6518 12.7877C55.6483 12.5389 54.7673 12.874 53.8888 13.3515C53.8888 13.3515 53.8112 13.8515 53.7274 13.9766C53.6436 14.1016 53.2481 14.0578 52.7088 14.3953C52.1695 14.7329 51.8328 14.8591 51.4136 15.1779C50.9944 15.4967 50.8956 15.7105 50.5377 15.8767C50.1798 16.043 49.7768 16.5018 49.5366 16.7518C49.2963 17.0018 48.4191 17.6194 47.3817 18.1269C46.3443 18.6345 42.0734 20.1521 41.0861 20.9522C39.3789 22.3589 37.512 23.5602 35.5238 24.5312L35.1947 24.695C35.0283 24.8475 32.6582 27.0127 31.846 27.579C31.0051 28.1653 30.6685 28.2503 30.4157 29.5929C30.2505 30.478 29.5736 31.9056 29.1068 33.0445C29.0192 33.9887 28.9566 34.9346 28.9191 35.8822C28.8866 36.7298 27.8004 45.158 27.5426 46.0693C27.2848 46.9807 27.216 47.667 27.3461 48.3833C27.4763 49.0996 27.1509 49.9472 26.7592 50.6635C26.3676 51.3798 25.7131 53.0749 25.8107 54.0525C25.9083 55.0301 26.0072 55.3889 25.7131 55.5189C25.419 55.6489 24.4705 55.6164 24.3366 55.9427C24.2027 56.269 23.7109 56.269 23.7109 56.269C23.7109 56.269 23.5645 57.7228 23.821 57.9779C24.0776 58.2329 24.9835 59.3042 23.8811 60.103C22.7786 60.9019 21.6149 61.6107 21.3433 62.1682C21.0718 62.7258 20.4674 63.0433 20.2409 63.2233C20.0144 63.4033 19.6064 63.6596 19.5751 63.7646C19.5439 63.8696 19.2135 64.0671 18.8956 63.9771C18.8956 63.9771 18.6241 64.0959 18.3363 63.7796C18.3363 63.7796 17.8695 63.8246 17.7782 63.5296C17.7782 63.5296 17.3853 63.5583 17.3402 63.2571C17.3402 63.2571 17.1437 62.8208 17.7331 62.6408C18.3225 62.4607 19.2698 61.1331 19.2948 60.9219C19.3199 60.7106 19.7704 59.3992 20.0957 58.8642C20.4211 58.3291 20.8653 56.904 20.9266 55.6139C20.889 55.5886 20.8574 55.5553 20.834 55.5164C20.8107 55.4775 20.7962 55.434 20.7915 55.3889C20.7866 55.0541 20.8109 54.7194 20.864 54.3888C20.9304 54.1488 21.0605 53.455 20.9742 52.4336C20.8878 51.4123 21.0993 49.7409 20.9742 48.5895C20.849 47.4382 21.0918 39.445 21.4397 38.4024C21.7876 37.3598 21.9189 37.1123 21.8977 36.6998C21.8764 36.2872 21.8977 35.0259 22.0228 34.9621C22.1479 34.8984 22.1755 33.3107 22.1317 33.1357C22.0879 32.9607 21.1519 32.3106 21.0217 31.8543C21.0328 32.0717 21.0328 32.2895 21.0217 32.5069C20.9992 32.6807 20.9779 32.4632 20.6288 32.0931C20.2797 31.7231 19.6277 30.9418 19.5839 30.3767C19.5051 30.3355 18.5403 29.7679 18.5403 29.7679C18.4995 29.7729 18.4582 29.7661 18.4212 29.7484C18.3841 29.7307 18.3529 29.7028 18.3313 29.6679H18.3363Z"/>
    <path d="M22.7885 20.0358C22.6896 21.801 18.5439 29.8367 18.5439 29.8367C18.4225 30.4193 18.4085 31.0192 18.5026 31.6068C18.6778 32.5532 17.7993 33.972 17.7993 33.972L18.7566 34.4721C20.048 33.722 19.4937 31.4906 19.4937 31.4906C19.3934 31.1169 19.3725 30.7264 19.4323 30.3442L19.4424 30.3542C19.4424 30.3542 24.145 21.0797 23.9172 20.3534C23.5311 20.2913 23.1532 20.1857 22.791 20.0383"/>
  </symbol>
  <symbol id="exception" aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 5 9 4V3m5 2 1-1V3m-3 6v11m0-11a5 5 0 0 1 5 5m-5-5a5 5 0 0 0-5 5m5-5a4.959 4.959 0 0 1 2.973 1H15V8a3 3 0 0 0-6 0v2h.027A4.959 4.959 0 0 1 12 9Zm-5 5H5m2 0v2a5 5 0 0 0 10 0v-2m2.025 0H17m-9.975 4H6a1 1 0 0 0-1 1v2m12-3h1.025a1 1 0 0 1 1 1v2M16 11h1a1 1 0 0 0 1-1V8m-9.975 3H7a1 1 0 0 1-1-1V8"/>
  </symbol>
  <svg id="images" aria-hidden="true" stroke="currentColor" fill="none" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m3 16 5-7 6 6.5m6.5 2.5L16 13l-4.286 6M14 10h.01M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
  </svg>
</svg>