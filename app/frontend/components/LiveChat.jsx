import React, { useState, useEffect, useRef } from "react";
import { createConsumer } from "@rails/actioncable";

export default function LiveChat() {
    const [isOpen, setIsOpen] = useState(false);
    const [messages, setMessages] = useState([]);
    const [newMessage, setNewMessage] = useState("");
    const [chatId, setChatId] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isConnected, setIsConnected] = useState(false);
    const messagesEndRef = useRef(null);
    const cableRef = useRef(null);
    const subscriptionRef = useRef(null);

    // Get domain and token from meta tags or environment
    const domain = window.location.hostname;
    const token = document.querySelector('meta[name="chat-token"]')?.content;

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // Initialize chat when component opens
    useEffect(() => {
        if (isOpen && !chatId) {
            initializeChat();
        }
    }, [isOpen]);

    // Setup WebSocket connection when chatId is available
    useEffect(() => {
        if (chatId && !subscriptionRef.current) {
            setupWebSocket();
        }
        return () => {
            if (subscriptionRef.current) {
                subscriptionRef.current.unsubscribe();
                subscriptionRef.current = null;
            }
        };
    }, [chatId]);

    const initializeChat = async () => {
        if (!token) {
            console.error("Chat token not found. Please add a meta tag with name='chat-token'");
            return;
        }

        setIsLoading(true);
        try {
            // Generate a unique external ID for this session
            const externalId = localStorage.getItem('chat_external_id') ||
                              `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            localStorage.setItem('chat_external_id', externalId);

            const response = await fetch('/api/v1/chats', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    'X-Widget-Domain': domain
                },
                body: JSON.stringify({ external_id: externalId })
            });

            if (response.ok) {
                const data = await response.json();
                setChatId(data.chat_id);
                await loadMessages(data.chat_id);
            } else {
                console.error('Failed to initialize chat:', response.statusText);
            }
        } catch (error) {
            console.error('Error initializing chat:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const loadMessages = async (chatIdToLoad) => {
        try {
            const response = await fetch(`/api/v1/chats/${chatIdToLoad}/messages`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'X-Widget-Domain': domain
                }
            });

            if (response.ok) {
                const data = await response.json();
                setMessages(data.messages || []);
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    };

    const setupWebSocket = () => {
        if (!cableRef.current) {
            cableRef.current = createConsumer();
        }

        subscriptionRef.current = cableRef.current.subscriptions.create(
            { channel: "ChatChannel", chat_id: chatId },
            {
                connected() {
                    console.log("Connected to ChatChannel");
                    setIsConnected(true);
                },
                disconnected() {
                    console.log("Disconnected from ChatChannel");
                    setIsConnected(false);
                },
                received(data) {
                    console.log("Received message:", data);
                    setMessages(prev => [...prev, {
                        content: data.content,
                        role: data.role,
                        created_at: data.created_at
                    }]);
                }
            }
        );
    };

    const sendMessage = async (e) => {
        e.preventDefault();
        if (!newMessage.trim() || !chatId || isLoading) return;

        const messageToSend = newMessage.trim();
        setNewMessage("");
        setIsLoading(true);

        try {
            const response = await fetch(`/api/v1/chats/${chatId}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                    'X-Widget-Domain': domain
                },
                body: JSON.stringify({ content: messageToSend })
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Failed to send message:', errorData);
                // Restore message if sending failed
                setNewMessage(messageToSend);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            // Restore message if sending failed
            setNewMessage(messageToSend);
        } finally {
            setIsLoading(false);
        }
    };

    const formatTime = (timeString) => {
        if (!timeString) return '';
        // If it's already formatted (HH:MM), return as is
        if (timeString.match(/^\d{2}:\d{2}$/)) return timeString;
        // Otherwise, format the date
        return new Date(timeString).toLocaleTimeString('ru-RU', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getRoleClass = (role) => {
        switch (role) {
            case 'client':
                return 'bg-blue-500 text-white ml-auto';
            case 'bot':
                return 'bg-gray-200 text-gray-800';
            case 'manager':
                return 'bg-green-200 text-green-800';
            default:
                return 'bg-gray-200 text-gray-800';
        }
    };

    const getRoleLabel = (role) => {
        switch (role) {
            case 'client': return 'Вы';
            case 'bot': return 'Бот';
            case 'manager': return 'Менеджер';
            default: return 'Система';
        }
    };

    if (!isOpen) {
        return (
            <button
                onClick={() => setIsOpen(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-4 shadow-lg transition-all duration-200 m-4"
                title="Открыть чат"
            >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
            </button>
        );
    }

    return (
        <div className="bg-white rounded-lg shadow-xl w-80 h-96 flex flex-col m-4 border">
            {/* Header */}
            <div className="bg-blue-500 text-white p-4 rounded-t-lg flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <h3 className="font-semibold">Чат поддержки</h3>
                    {isConnected && (
                        <div className="w-2 h-2 bg-green-400 rounded-full" title="Подключено"></div>
                    )}
                </div>
                <button
                    onClick={() => setIsOpen(false)}
                    className="text-white hover:text-gray-200 transition-colors"
                >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {isLoading && messages.length === 0 ? (
                    <div className="text-center text-gray-500">Загрузка...</div>
                ) : (
                    messages.map((message, index) => (
                        <div key={index} className="flex flex-col">
                            <div className={`max-w-xs p-3 rounded-lg ${getRoleClass(message.role)}`}>
                                <div className="text-xs opacity-75 mb-1">
                                    {getRoleLabel(message.role)}
                                </div>
                                <div className="text-sm">{message.content}</div>
                                <div className="text-xs opacity-75 mt-1">
                                    {formatTime(message.created_at)}
                                </div>
                            </div>
                        </div>
                    ))
                )}
                <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <form onSubmit={sendMessage} className="p-4 border-t">
                <div className="flex gap-2">
                    <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Введите сообщение..."
                        className="flex-1 border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={isLoading || !chatId}
                    />
                    <button
                        type="submit"
                        disabled={isLoading || !newMessage.trim() || !chatId}
                        className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    );
}
