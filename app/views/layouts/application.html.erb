<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Live Chat" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>
    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%= vite_client_tag %>
    <%= vite_react_refresh_tag %>
    <%= vite_stylesheet_tag 'application' %>
    <%= vite_javascript_tag 'application' %>
    <%= vite_javascript_tag 'live-chat', defer: true %>
  </head>

  <body>
    <header id="header">
      <%= render '/layouts/partials/notices/notices' %>
      <%#= render partial: '/layouts/partials/header' %>
    </header>

    <main>
      <div class="container mx-auto px-5">
        <%= yield %>
      </div>
    </main>
    <style>
        #live-chat-container {
            position: fixed;
            z-index: 10000;
            right: 0;
            bottom: 0;
        }
    </style>
    <div id="live-chat-container"></div>
  </body>
</html>
