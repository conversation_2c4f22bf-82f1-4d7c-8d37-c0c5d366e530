<% [notices].flatten.each do |notice| %>
  <div data-controller="notices" data-notices-text-value="<%= notice %>"  class="flex items-center w-full max-w-xs p-2 mb-2 rounded-lg shadow bg-white notices" role="alert">
    <div>
      <% key = 'error' if %w[alert danger].include? key %>
      <% if %w[error warning].include? key %>
        <%= render IconComponent.new name: key %>
      <% else %>
        <%= render IconComponent.new name: :success %>
      <% end %>
    </div>
    <div class="ms-3 text-sm font-normal"><%= notice %></div>
    <button data-action="click->notices#close" type="button" class="ms-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex items-center justify-center h-8 w-8 bg-white focus:text-green-600 close" data-dismiss-target="#toast-danger" aria-label="Close">
      <span class="sr-only">Close</span>
      <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
      </svg>
    </button>
  </div>
<% end %>
