<nav class="nav-bar bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700">
  <div class="nav-wrapper">
    <div class="flex justify-between items-center">
      <div class="flex items-center justify-start">
        <button class="btn-menu text-gray-600 dark:text-gray-400" data-action="click->menu-btn#showMenu">
          <svg class="w-6 h-6 burger" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
          </svg>
          <svg class="w-6 h-6 close" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
        <%= link_to '/admin' do %>
          <span>Admin</span>
        <% end %>
        <div style="height: 46px;"></div>
      </div>
      <div class="flex items-center text-gray-900 dark:text-white">
        <button id="theme-toggle" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5">
          <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>
          <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path></svg>
        </button> 

        <div data-controller="dropdown" class="relative">
          <button data-action="click->dropdown#toggle" class="h-8 w-8 flex bg-red-200 dark:bg-red-500 text-gray-900 dark:text-white px-3 py-3 items-center justify-center ml-3 text-sm rounded-full md:me-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" type="button">
            <div class="uppercase"><%= current_user.email&.at(0) %></div>
          </button>

          <!-- Dropdown menu -->
          <div data-dropdown-target="menu" class="absolute overflow-hidden right-0 top-10 z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
            <div class="text-sm text-gray-900 dark:text-white">
              <%= link_to admin_user_path(current_user), class: 'block px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white' do %>
                <div class="font-medium truncate"><%= current_user.email %></div>
                <div><%= t("roles.#{current_user.role}") %></div>
              <% end %>
            </div>
            <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownUserAvatarButton">
              <li>
                <a href="/admin" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Аналитика</a>
              </li>
              <li>
                <a href="/admin/settings" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Настройки</a>
              </li>
            </ul>
            <%= button_to "Выйти", destroy_user_session_path, method: :delete, class: 'py-2 w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white' %>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>
