<!DOCTYPE html>
<html lang="ru">
<html>
  <head>
    <title><%= "#{content_for(:title).present? ? "#{content_for(:title)} | " : ''}#{settings[:app_name]&.capitalize} – Admin panel" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= vite_client_tag %>
    <%= vite_stylesheet_tag 'admin' %>
    <%= vite_javascript_tag 'admin' %>
  </head>

  <body class="bg-white dark:bg-gray-900 antialiased" data-controller="menu-btn">
    <header id="header">
      <%= render '/layouts/partials/notices/notices' %>
      <%= render partial: '/layouts/partials/admin/header' %>
    </header>

    <div class="body" id="body">
      <aside id="sidebar" class="sidebar"><%= render partial: '/layouts/partials/admin/aside' %></aside>

      <div class="sidebarBackdrop" data-action="click->menu-btn#showMenu"></div>

      <div class="main-content" id="main-content" data-menu-btn-target="body">
        <main>
          <%= yield %>
        </main>

        <%= render partial: '/layouts/partials/admin/footer' %>
      </div>
    </div>

    <%= render partial: '/layouts/partials/admin/modal' %>
  </body>
</html>
