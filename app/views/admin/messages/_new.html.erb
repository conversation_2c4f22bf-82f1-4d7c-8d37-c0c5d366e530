<%= form_with model: @message, url: admin_messages_path, class: "max-w-lg mx-auto" do |form| %>
  <%= form.hidden_field :user_id, value: @message.user.id %>

  <div class="mb-5">
    <%= form.label :who, "Кому",
                   class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
    <%= form.text_field :who, value: @message.user.full_name, readonly: true,
                        class: "shadow-sm bg-gray-50 border border-gray-300 focus:ring-gray-300 focus:border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-gray-600 dark:focus:border-gray-600 dark:shadow-sm-light" %>
  </div>

  <div class="mb-5">
    <%= form.label :text, "Сообщение",
                   class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white"  %>
    <%= form.text_area :text, rows: 4, autofocus: true,
                       class: "block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" %>
  </div>

  <div class="mb-5">
    <%= form.label :attachment, class: 'block mb-2 text-sm font-medium text-gray-900 dark:text-white' %>
    <%= form.file_field :attachment, accept: 'image/jpeg,image/png,image/webp,video/mp4,video/mpeg,video/quicktime',
                        class: 'shadow-sm bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:shadow-sm-light' %>
  </div>

  <%= form.submit "Отправить", class: blue_btn_styles, data: { action: "click->modal#close" } %>
<% end %>
