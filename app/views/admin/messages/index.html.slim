- content_for :title, 'Сообщения'

.pt-6.px-4
  h1 class="text-xl leading-8 font-semibold text-gray-900 dark:text-white mb-4" = content_for :title

.pt-6
  .relative.overflow-x-auto.shadow-md.sm:rounded-lg
    .flex.rounded-lg.shadow-lg.overflow-hidden.h-[600px]
      .w-1/3.border-r.flex.flex-col.bg-white.border-gray-200.dark:bg-gray-800.dark:border-gray-700
        .px-4.py-3.text-xl.font-bold.text-gray-700
          | Чаты

        .flex-1.overflow-y-auto
          - @chats.each do |chat|
            = link_to admin_messages_path(chat_id: chat.id), class: "block px-4 py-3 hover:bg-blue-50 transition #{'bg-blue-100 font-bold' if chat.id == @current_chat_id} rounded" do
              .flex.items-center.gap-2.justify-between
                div
                  .text-sm.text-gray-500 = chat.chat_widget.name
                  .flex.items-center.space-x-2
                    .w-3.h-3.rounded-full.bg-green-400
                    span.text-gray-700 = "Чат №#{chat.id}"
                div
                  time data-controller="timeago" data-timeago-locale-value="ru" title="#{format_date chat.messages.last.created_at}" datetime="#{chat.messages.last.created_at.iso8601}"
                    = format_date chat.messages.last.created_at

      .flex-1.flex.flex-col data-controller="clear-input"
        .px-6.py-4.border-b.bg-gray-50
          - if @current_chat
            .text-lg.font-semibold.text-gray-800 = "Чат №#{@current_chat.id}"
          - else
            .text-lg.text-gray-400.italic Нет чата для просмотра

        = turbo_stream_from "chat_#{@current_chat&.id}"
        .overflow-y-auto.p-4 class="max-h-[400px]" id='messages' data-clear-input-target="messages"
          - if @messages.present?
            = render partial: '/layouts/partials/admin/pagy'
            - @messages.each do |message|
              = render partial: '/admin/messages/msg', locals: { message: message }
          - else
            .text-gray-400.text-center.pt-10 Нет сообщений

        - if @current_chat
          = form_with url: admin_messages_path, method: :post, class: "flex items-center gap-2 p-4 border-t bg-gray-50", data: { action: 'submit->clear-input#clear' } do |f|
            = f.hidden_field :chat_id, value: @current_chat.id
            = f.text_field :content, placeholder: "Введите сообщение...", class: "flex-1 rounded border-gray-300", data: { clear_input_target: 'input' }
            = f.button "Отправить", class: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
