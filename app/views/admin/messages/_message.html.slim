tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" id="#{dom_id message}"
  td.w-4.p-4
    .flex.items-center
      input id="checkbox-table-search-1" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
      label for="checkbox-table-search-1" class="sr-only" checkbox
  td.px-6.py-4 = message.chat_id
  td.px-6.py-4 = message.content

  td.px-6.py-4
    time data-controller="timeago" data-timeago-locale-value="ru" title="#{format_date message.created_at}" datetime="#{message.created_at.iso8601}"
      = format_date message.created_at

  td.flex.items-center.px-6.py-4
    = button_to admin_message_path(message), method: :delete, class: 'btn btn-danger', data: { confirm: 'Вы уверены, что хотите удалить данное сообщение?', controller: 'confirm', action: 'click->confirm#call' } do
      = render Admin::IconComponent.new name: :trash
