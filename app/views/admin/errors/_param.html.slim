li.border-x.border-b.border-gray-200.bg-white.dark:bg-gray-800.dark:border-gray-700.hover:bg-gray-50.dark:hover:bg-gray-600
  .flex.flex-col.sm:flex-row
    span.flex.flex-row.items-center.justify-between.sm:w-1/2
      span class="hidden sm:flex sm:w-1/3"
        div class="text-md inline-flex items-center px-4 border-gray-400 border-r-2 sm:border-r-0 sm:border-l-2 mx-2"
          div class="flex items-center justify-center" data-state="tooltip-hidden" data-reach-tooltip-trigger=""
            div class="h-6 text-sm" N/A
      / = "#{key}.#{k}.#{g}"
      .break-words.p-2.sm:w-2/3.sm:p-3 = key
      span class="flex justify-end sm:hidden"
        div class="text-md inline-flex items-center px-4 border-gray-400 border-r-2 sm:border-r-0 sm:border-l-2 mx-2"
          div class="flex items-center justify-center" data-state="tooltip-hidden" data-reach-tooltip-trigger=""
            div class="h-6 text-sm" N/A
    .items-center.break-words.p-2.pt-0.font-code.text-xs.sm:w-1/2.sm:px-3.sm:py-4
      - if key == 'request.body'
        - (JSON.parse value).each do |k, v|
          .mb-1 = "#{k} => #{v}"
      - else
        = value
