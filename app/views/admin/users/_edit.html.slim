= form_with(model: @user, url: admin_user_path(@user), method: :patch,
        class: 'admin-form', data: { action: 'submit->modal#close' }) do |form|
  / .mb-5
  /   = form.label :middle_name
  /   = form.text_field :middle_name
  .mb-5
    = form.label :first_name
    = form.text_field :first_name
  / .mb-5
  /   = form.label :last_name
  /   = form.text_field :last_name
  .mb-5
    = form.label :email
    = form.text_field :email
  .flex.flex-wrap class="-mx-1.5"
    .mb-5.w-full.sm:w-1/2.px-1.5
      = form.label :tg_id
      = form.number_field :tg_id
    .mb-5.w-full.sm:w-1/2.px-1.5
      = form.label :username
      = form.text_field :username
  .mb-5
    = form.label :address
    = form.text_field :address
  .mb-5
    = form.label :street
    = form.text_field :street
  .flex.flex-wrap class="-mx-1.5"
    .mb-5.w-full.sm:w-1/3.px-1.5
      = form.label :home
      = form.text_field :home
    .mb-5.w-full.sm:w-1/3.px-1.5
      = form.label :apartment
      = form.text_field :apartment
    .mb-5.w-full.sm:w-1/3.px-1.5
      = form.label :build
      = form.text_field :build
  .flex.flex-wrap class="-mx-1.5"
    .mb-5.w-full.sm:w-1/2.px-1.5
      = form.label :phone_number
      = form.text_field :phone_number
    / .mb-5.w-full.sm:w-1/2.px-1.5
    /   = form.label :postal_code
    /   = form.number_field :postal_code
  / - if current_user.admin?
  /   .mb-5
  /     = form.label :role
  /     = form.select :role, User.roles.keys.map { |key| [I18n.t("roles.#{key}"), key] }, {}
  - if settings[:bonus_threshold].to_i.positive?
    .flex.flex-wrap class="-mx-1.5"
      .mb-5.w-1/2.px-1.5
        = form.label :account_tier_id
        = form.select :account_tier_id, [['Гость', nil]] + AccountTier.all.map { |i| [i.title, i.id] }, {}
      .mb-5.w-1/2.px-1.5
        = form.label :bonus_balance
        = form.number_field :bonus_balance, value: nil
        .text-sm.text-red-800.mt-1 Текущий баланс: #{@user.bonus_balance}
  - else
    p.mb-5.dark:text-white.text-xs
      | Бонусная программа не активна, что бы активировать добавьте в настройках bonus_threshold(минимальный порог заказа для начисления бонусов) и укажите уровни программы лояльности
  .flex.items-center
    = form.submit "Сохранить"
