- content_for :title, 'Пользователи'

.pt-6.px-4
  h1 class="text-xl leading-8 font-semibold text-gray-900 dark:text-white mb-4" = yield(:title)

  = search_form_for @q_users, url: admin_users_path, method: :get, class: "max-w-md mx-auto" do |f|
    .relative
      = f.search_field :email_cont_any, class: 'block w-full p-4 pe-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
      = button_tag type: 'submit', class: 'text-white absolute end-2 bottom-2.5 font-medium rounded-lg text-sm p-2' do
        svg class="w-4 h-4 text-gray-900 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20"
          path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"

#users class="pt-6"
  div class="relative overflow-x-auto shadow-md sm:rounded-lg"
    table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400"
      thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400"
        tr
          th scope="col" class="p-4"
            .flex.items-center
              input id="checkbox-all-search" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              label for="checkbox-all-search" class="sr-only" checkbox
          th scope="col" class="px-6 py-3"
            = sort_link(@q_users, :id, 'ИД', data: { turbo_prefetch: false })
          th scope="col" class="px-6 py-3"
            = sort_link(@q_users, :email, 'Почта', data: { turbo_prefetch: false })
          th scope="col" class="px-6 py-3"
            = sort_link(@q_users, :created_at, 'Дата регистрации', data: { turbo_prefetch: false })
          th scope="col" class="px-6 py-3"
            = sort_link(@q_users, :role, 'Роль', data: { turbo_prefetch: false })
          th scope="col" class="px-6 py-3" Действия

      tbody = render @users

  = render partial: '/layouts/partials/admin/pagy'
