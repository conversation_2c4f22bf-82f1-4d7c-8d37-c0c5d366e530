- content_for :title, 'Виджеты чата'

.pt-6.px-4
  h1 class="text-xl leading-8 font-semibold text-gray-900 dark:text-white mb-4" = yield(:title)

  = link_to 'Создать', new_admin_chat_widget_path, class: 'btn',
                data: { controller: 'modal-btn', action: 'click->modal-btn#open', turbo_prefetch: false }

#chat_widgets class="pt-6"
  div class="relative overflow-x-auto shadow-md sm:rounded-lg"
    table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400"
      thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400"
        tr
          th scope="col" class="p-4"
            .flex.items-center
              input id="checkbox-all-search" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              label for="checkbox-all-search" class="sr-only" checkbox
          th scope="col" class="px-6 py-3" ИД
          th scope="col" class="px-6 py-3" Название
          th scope="col" class="px-6 py-3" Домен
          th scope="col" class="px-6 py-3" Доступ
          th scope="col" class="px-6 py-3" Настройки
          th scope="col" class="px-6 py-3" Дата создания
          th scope="col" class="px-6 py-3" Действия
      tbody = render @chat_widgets
