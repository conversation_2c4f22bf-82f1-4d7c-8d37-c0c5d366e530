ru:
  pagy:
    aria_label:
      nav:
        one: "Страница"
        few: "Страницы"
        many: "Страниц"
        other: "Страниц"
      prev: "Назад"
      next: "Вперёд"
    prev: "&lt;"
    next: "&gt;"
    gap: "&hellip;"
    item_name:
      one: "запись"
      few: "записи"
      many: "записей"
      other: "записей"
    info:
      no_items: "Пока %{item_name} нет"
      single_page: "%{count} %{item_name}"
      multiple_pages: "Всего %{count} %{item_name}, показаны с %{from} по %{to}"
    combo_nav_js: "Страница %{page_input} из %{pages}"
    limit_selector_js: "Показать %{limit_input} %{item_name} на странице"
  roles:
    user: "Пользователь"
    manager: "Мен<PERSON>джер"
    moderator: "Модератор"
    admin: "Администратор"
  activerecord:
    models:
      user: "Пользователь"
    attributes:
      setting:
        variable: "Переменная"
        value: "Значение"
        description: "Описание"
      user:
        created_at: "Дата регистрации"
        password: "Пароль"
        email: "Электронная почта"
        middle_name: "Фамилия"
        first_name: "Имя"
        role: "Роль"
    errors:
      models:
        user:
          attributes:
            email:
              taken: "уже используется"
