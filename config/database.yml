default: &default
  adapter: postgresql
  encoding: unicode
  host: <%= ENV.fetch('DB_HOST', 'localhost') %>
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: live_chat_development

test:
  <<: *default
  database: live_chat_test

production:
  primary: &primary_production
    <<: *default
    database: <%= ENV.fetch('DB_NAME', 'live_chat_production') %>
    username: <%= ENV.fetch('DB_USER') %>
    password: <%= ENV.fetch('DB_PASSWORD') %>
  cache:
    <<: *primary_production
    database: live_chat_production_cache
    migrations_paths: db/cache_migrate
  queue:
    <<: *primary_production
    database: live_chat_production_queue
    migrations_paths: db/queue_migrate
  cable:
    <<: *primary_production
    database: live_chat_production_cable
    migrations_paths: db/cable_migrate
