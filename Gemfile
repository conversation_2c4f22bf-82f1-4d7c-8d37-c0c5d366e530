source "https://rubygems.org"

gem "rails", "~> 8.0.2"

gem "bootsnap", require: false
gem "puma", ">= 6.0"
gem "turbo-rails"
gem "stimulus-rails"
gem 'sprockets-rails'
gem "jbuilder"
gem "tzinfo-data", platforms: %i[windows jruby]

gem "devise"
gem "pg", "~> 1.6"
# gem 'redis', '~> 5.4'

gem 'active_link_to'
gem "bcrypt"
gem 'faraday'
gem 'slim-rails'
gem 'view_component'
gem 'pagy'
gem 'pundit'
gem 'ransack'
gem 'vite_rails'
gem 'dotenv'
gem 'i18n'
gem 'rack-cors'
gem 'telegram-bot-ruby', require: false

gem 'solid_queue'
gem "solid_queue_dashboard"
gem 'solid_cache'
gem 'solid_cable'

group :development, :test do
  gem "pry"
  gem "brakeman", require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rails', require: false
end

group :development do
  gem 'better_errors'
  gem 'binding_of_caller'
  gem 'bullet'
  gem 'letter_opener'
  # gem 'solargraph'
  # gem 'sshkit'
  gem 'web-console'
end
